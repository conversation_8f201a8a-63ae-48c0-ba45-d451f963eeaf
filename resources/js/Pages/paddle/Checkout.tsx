import React, { useEffect, useState } from 'react';
import { Head } from '@inertiajs/react';
import { PaddleProvider, usePaddle } from '@/contexts/PaddleContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface PaddleCheckoutProps {
    transaction_id?: string;
    error?: string;
}

function PaddleCheckoutContent({ transaction_id, error }: PaddleCheckoutProps) {
    const [isLoading, setIsLoading] = useState(true);
    const [checkoutError, setCheckoutError] = useState<string | null>(error || null);
    const { paddle, isLoaded, isConfigured } = usePaddle();

    useEffect(() => {
        // If there's an initial error, stop loading
        if (error) {
            setIsLoading(false);
            return;
        }

        // Wait for Paddle to be loaded and configured
        if (!isLoaded || !isConfigured) {
            return;
        }

        // If we have a transaction ID, open the checkout
        if (transaction_id && paddle) {
            try {
                setIsLoading(true);
                
                // Open Paddle checkout for the transaction
                paddle.Checkout.open({
                    transactionId: transaction_id,
                    settings: {
                        displayMode: 'overlay',
                        theme: 'light',
                        locale: 'en',
                        successUrl: `${window.location.origin}/paddle/success`,
                        closeUrl: `${window.location.origin}/subscription/checkout`,
                    }
                });

                setIsLoading(false);
            } catch (err) {
                console.error('Failed to open Paddle checkout:', {
                    error: err,
                    transactionId: transaction_id,
                    paddleAvailable: !!paddle,
                    errorMessage: err instanceof Error ? err.message : 'Unknown error'
                });
                setCheckoutError('Failed to open checkout. Please try again.');
                setIsLoading(false);
                toast.error('Failed to open checkout. Please try again.');
            }
        } else if (!transaction_id) {
            console.error('No transaction ID provided to checkout page');
            setCheckoutError('No transaction ID provided.');
            setIsLoading(false);
        } else if (!paddle) {
            console.error('Paddle instance not available');
            setCheckoutError('Payment system not ready. Please try again.');
            setIsLoading(false);
        }
    }, [isLoaded, isConfigured, transaction_id, paddle, error]);

    const handleRetry = () => {
        window.location.href = '/subscription/checkout';
    };

    const handleGoHome = () => {
        window.location.href = '/';
    };

    if (checkoutError) {
        return (
            <>
                <Head title="Checkout Error" />
                <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
                    <Card className="w-full max-w-md">
                        <CardHeader className="text-center">
                            <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
                                <AlertCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
                            </div>
                            <CardTitle className="text-red-900 dark:text-red-100">
                                Checkout Error
                            </CardTitle>
                            <CardDescription className="text-red-700 dark:text-red-300">
                                {checkoutError}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Button 
                                onClick={handleRetry}
                                className="w-full"
                                variant="default"
                            >
                                Try Again
                            </Button>
                            <Button 
                                onClick={handleGoHome}
                                className="w-full"
                                variant="outline"
                            >
                                Go Home
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </>
        );
    }

    return (
        <>
            <Head title="Loading Checkout" />
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
                <Card className="w-full max-w-md">
                    <CardHeader className="text-center">
                        <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4">
                            {isLoading ? (
                                <Loader2 className="w-6 h-6 text-blue-600 dark:text-blue-400 animate-spin" />
                            ) : (
                                <CreditCard className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            )}
                        </div>
                        <CardTitle className="text-gray-900 dark:text-gray-100">
                            {isLoading ? 'Loading Checkout...' : 'Opening Checkout'}
                        </CardTitle>
                        <CardDescription>
                            {isLoading 
                                ? 'Please wait while we prepare your checkout session.'
                                : 'Your checkout should open shortly.'
                            }
                        </CardDescription>
                    </CardHeader>
                    {isLoading && (
                        <CardContent>
                            <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                                This may take a few seconds...
                            </div>
                        </CardContent>
                    )}
                </Card>
            </div>
        </>
    );
}

export default function PaddleCheckout(props: PaddleCheckoutProps) {
    return (
        <PaddleProvider>
            <PaddleCheckoutContent {...props} />
        </PaddleProvider>
    );
}
